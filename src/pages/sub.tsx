import LegoRender from '@blm/bi-lego-sdk/LegoRender';
import { report } from '@blm/bi-lego-sdk/utils';
import React from 'react';
import dayjs from 'dayjs';

window.report = report;

const filterProps = {
  adcode: {
    // 默认值
    defaultValue: ['110100'],
    // clearable: true,
  },
  senior_manager_name: {
    defaultValue: ['杜乐乐'],
  },
  car_team_name: {
    defaultValue: ['长安汽车'],
    options: [
      {
        label: '长安汽车',
        value: '长安汽车',
      },
      {
        label: '比亚迪',
        value: '比亚迪',
      },
    ],
    // defaultValue: ['长安汽车'],
    // clearable: true,
  },
  tenant_name: {
    defaultValue: ['约约出行'],
  },
  dur_online: {
    defaultValue: [0, 100],
  },
  // t_date: {
  //   defaultValue: {
  //     category: 'custom',
  //     dateType: 'customDate',
  //     value: [
  //       // new Date('2025-07-03').toString(),
  //       dayjs('2015/01/01', 'YYYY/MM/DD'),
  //       new Date('2025-07-11').toString(),
  //     ],
  //   },
  // },
  日筛选: {
    defaultValue: {
      category: 'custom',
      dateType: 'customDate',
      // value: [
      //   new Date('2025-06-03').toString(),
      //   new Date('2025-06-11').toString(),
      // ],
      value: [
        dayjs('2015/01/01', 'YYYY/MM/DD'),
        dayjs('2015/02/01', 'YYYY/MM/DD'),
      ],
    },
  },
};
export default () => {
  const [show, setShow] = React.useState(false);
  return (
    <>
      <button onClick={() => setShow(!show)}>切换</button>
      {show && (
        <LegoRender
          filterProps={filterProps}
          reportId="1780498439921533691"
        ></LegoRender>
      )}
      <LegoRender
        filterProps={filterProps}
        reportId="1780498439921534211"
      ></LegoRender>
      {/* <LegoRender
        // filterProps={filterProps}
        reportKey="financeBusinessAnalysis"
      ></LegoRender> */}
    </>
  );
};
